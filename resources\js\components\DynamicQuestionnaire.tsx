import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { CheckCircle, Circle } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';

interface Question {
    id: string;
    question?: string;
    label?: string;
    type: 'text' | 'textarea' | 'radio';
    required: boolean;
    options?: string[];
    max_length?: number;
    placeholder?: string;
}

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    questions: Question[];
}

interface DynamicQuestionnaireProps {
    template: QuestionnaireTemplate;
    initialData?: { [key: string]: any };
    onSubmit: (data: { [key: string]: any }) => void;
    onCancel: () => void;
    isSubmitting?: boolean;
}

export default function DynamicQuestionnaire({ template, initialData = {}, onSubmit, onCancel, isSubmitting = false }: DynamicQuestionnaireProps) {
    const [formData, setFormData] = useState<{ [key: string]: any }>(initialData);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    useEffect(() => {
        setFormData(initialData);
    }, [initialData]);

    // 檢查是否可以提交
    const canSubmit = useMemo(() => {
        return template.questions.every((question) => {
            if (!question.required) return true;
            const value = formData[question.id];
            return value && (typeof value !== 'string' || value.trim() !== '');
        });
    }, [formData, template.questions]);

    const handleInputChange = (questionId: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [questionId]: value,
        }));

        // Clear error when user starts typing
        if (errors[questionId]) {
            setErrors((prev) => {
                const newErrors = { ...prev };
                delete newErrors[questionId];
                return newErrors;
            });
        }
    };

    const validateForm = (): boolean => {
        const newErrors: { [key: string]: string } = {};

        template.questions.forEach((question) => {
            if (question.required) {
                const value = formData[question.id];
                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    newErrors[question.id] = '此欄位為必填';
                }
            }

            // Check max length for text fields
            if (question.max_length && formData[question.id]) {
                const value = formData[question.id].toString();
                if (value.length > question.max_length) {
                    newErrors[question.id] = `內容不能超過 ${question.max_length} 字`;
                }
            }
        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (validateForm()) {
            onSubmit(formData);
        }
    };

    const renderQuestion = (question: Question, index: number) => {
        const value = formData[question.id] || '';
        const error = errors[question.id];
        const isAnswered = value && (typeof value !== 'string' || value.trim() !== '');
        const questionNumber = index + 1;

        switch (question.type) {
            case 'text':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>
                            <div className="flex-1 space-y-3">
                                <div className="flex items-center gap-2">
                                    {/* todo 調整UI，擴充為中文問題label 與英文問題label */}
                                    <Label htmlFor={question.id} className="text-base font-semibold text-gray-800">
                                        {question.question || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                </div>

                                <Input
                                    id={question.id}
                                    type="text"
                                    value={value}
                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                    placeholder={question.placeholder}
                                    maxLength={question.max_length}
                                    className={`${error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'}`}
                                />
                                <div className="flex items-center justify-between">
                                    {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                                    {question.max_length && (
                                        <div className="ml-auto text-sm text-gray-500">
                                            {value.length}/{question.max_length}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                );

            case 'textarea':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>
                            <div className="flex-1 space-y-3">
                                <div className="flex items-center gap-2">
                                    <Label htmlFor={question.id} className="text-base font-semibold text-gray-800">
                                        {question.question || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                </div>
                                <Textarea
                                    id={question.id}
                                    value={value}
                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                    placeholder={question.placeholder}
                                    maxLength={question.max_length}
                                    className={`min-h-[120px] resize-none ${error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'}`}
                                />
                                <div className="flex items-center justify-between">
                                    {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                                    {question.max_length && (
                                        <div className="ml-auto text-sm text-gray-500">
                                            {value.length}/{question.max_length}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                );

            case 'radio':
                return (
                    <div key={question.id} className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                        <div className="flex items-start gap-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-bold text-blue-600">
                                {questionNumber}
                            </div>
                            <div className="flex-1 space-y-3">
                                <div className="flex items-center gap-2">
                                    <Label className="text-base font-semibold text-gray-800">
                                        {question.question || question.label}
                                        {question.required && <span className="ml-1 text-red-500">*</span>}
                                    </Label>
                                    {isAnswered && <CheckCircle className="h-5 w-5 text-green-500" />}
                                    {!isAnswered && <Circle className="h-5 w-5 text-gray-300" />}
                                </div>
                                <div className="grid gap-3 md:grid-cols-5">
                                    {question.options?.map((option, optionIndex) => {
                                        const isSelected = value === option;
                                        const isFirst = optionIndex === 0;
                                        const isLast = optionIndex === (question.options?.length ?? 0) - 1;

                                        const bgClass = isSelected
                                            ? 'border-blue-500 bg-blue-50'
                                            : `border-gray-200 ${isFirst ? 'bg-red-50' : isLast ? 'bg-green-50' : ''}`;

                                        return (
                                            <label
                                                key={optionIndex}
                                                className={`flex cursor-pointer items-center space-x-3 rounded-lg border p-3 transition-colors hover:bg-blue-50 ${bgClass}`}
                                            >
                                                <input
                                                    type="radio"
                                                    name={question.id}
                                                    value={option}
                                                    checked={isSelected}
                                                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                                                    className="text-blue-600 focus:ring-blue-500"
                                                />
                                                <span className="text-sm font-medium text-gray-700">{option}</span>
                                            </label>
                                        );
                                    })}
                                </div>
                                {error && <div className="text-sm font-medium text-red-500">{error}</div>}
                            </div>
                        </div>
                    </div>
                );

            default:
                return <>?</>;
        }
    };

    return (
        <div className="w-full space-y-6">
            {/* 問卷標題 */}
            <div className="rounded-lg bg-gradient-to-r from-green-50 to-blue-50 p-6">
                <div className="space-y-4">
                    <div>
                        <h2 className="text-xl font-bold text-gray-900">{template.template_name}</h2>
                        <p className="text-base text-gray-600">
                            {template.department_name} – {template.program_type}
                        </p>
                    </div>
                </div>
            </div>

            {/* 問卷內容 */}
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-6">{template.questions.map((question, index) => renderQuestion(question, index))}</div>

                {/* 提交按鈕區域 */}
                <div className="sticky bottom-0 flex items-center justify-between gap-4 rounded-lg border-t bg-white p-6 shadow-lg">
                    <div className="text-sm text-gray-600">
                        {/* todo 加入提示文字(提示推薦人當前正在處裡的問卷是哪位考生的，僅提示考生姓名與報考項目) */}
                        {canSubmit ? (
                            <span className="flex items-center gap-2 text-green-600">
                                <CheckCircle className="h-4 w-4" />
                                所有必填題目已完成，可以提交
                            </span>
                        ) : (
                            <span className="text-amber-600">
                                還有{' '}
                                {
                                    template.questions.filter(
                                        (q) =>
                                            q.required && (!formData[q.id] || (typeof formData[q.id] === 'string' && formData[q.id].trim() === '')),
                                    ).length
                                }{' '}
                                個必填題目未完成
                            </span>
                        )}
                    </div>
                    <div className="flex gap-3">
                        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
                            取消
                        </Button>
                        <Button type="submit" disabled={!canSubmit || isSubmitting} className="min-w-[100px]">
                            {isSubmitting ? '提交中...' : '提交問卷'}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}
