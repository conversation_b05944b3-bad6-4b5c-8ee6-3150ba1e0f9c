import { useLanguage } from '@/hooks/use-language';
import AppLogoIcon from './app-logo-icon';

export default function AppLogo() {
    const { t } = useLanguage();
    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md text-sidebar-primary-foreground">
                <AppLogoIcon />
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                {/* 大裝置 */}
                <span className="mb-0.5 hidden truncate leading-tight font-semibold sm:inline">
                    {/* todo - 放入招生訊息(考生在登入時會向原系統查詢的報名資訊中會夾帶必要參數) ex: xx大學 – xx班招生 */}
                    {t('common.schoolName')} – xxx
                </span>

                {/* 小裝置 */}
                <span className="mb-0.5 truncate leading-tight font-semibold sm:hidden">
                    {/* todo - 放入招生訊息 ex: xx大學 – xx班招生 */}
                    {t('common.schoolName_short')} – xxx
                </span>
            </div>
        </>
    );
}
