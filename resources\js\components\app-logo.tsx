import { useLanguage } from '@/hooks/use-language';
import { SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import AppLogoIcon from './app-logo-icon';

export default function AppLogo() {
    const { t } = useLanguage();
    const { admission_info, applications, auth } = usePage<SharedData>().props;

    // 獲取招生訊息的函數
    const getRecruitmentInfo = () => {
        // 優先使用 admission_info 中的招生訊息
        if (admission_info && admission_info.length > 0) {
            // 取第一個有效的招生訊息
            const activeAdmission = admission_info[0];
            return activeAdmission.exam_name;
        }

        // 如果是申請人且有報名資料，使用報名資料中的招生訊息
        if (auth?.user?.role === 'applicant' && applications && applications.length > 0) {
            // 取第一個報名項目的招生訊息
            const firstApplication = applications[0];
            return firstApplication.exam_name;
        }

        // 回退到預設訊息
        return '推薦函系統';
    };

    const recruitmentInfo = getRecruitmentInfo();

    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md text-sidebar-primary-foreground">
                <AppLogoIcon />
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                {/* 大裝置 */}
                <span className="mb-0.5 hidden truncate leading-tight font-semibold sm:inline">
                    {t('common.schoolName')} – {recruitmentInfo}
                </span>

                {/* 小裝置 */}
                <span className="mb-0.5 truncate leading-tight font-semibold sm:hidden">
                    {t('common.schoolName_short')} – {recruitmentInfo}
                </span>
            </div>
        </>
    );
}
